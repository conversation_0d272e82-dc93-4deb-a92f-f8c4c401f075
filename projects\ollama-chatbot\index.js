const express = require('express');
const { Ollama } = require('ollama');
const path = require('path');

const app = express();
const port = 3000;

// Ollama istemcisini başlat
const ollama = new Ollama({ host: 'http://localhost:11434' });

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Ana sayfa
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Chat endpoint
app.post('/chat', async (req, res) => {
    try {
        const { message, model = 'qwen3:4b' } = req.body;
        
        if (!message) {
            return res.status(400).json({ error: 'Mesaj gerekli' });
        }

        console.log(`Kullanıcı mesajı: ${message}`);
        
        // Ollama'ya mesaj gönder
        const response = await ollama.chat({
            model: model,
            messages: [{ role: 'user', content: message }],
            stream: false
        });

        const botResponse = response.message.content;
        console.log(`Bot yanıtı: ${botResponse}`);

        res.json({ 
            response: botResponse,
            model: model 
        });

    } catch (error) {
        console.error('Hata:', error);
        
        if (error.message.includes('model')) {
            res.status(400).json({ 
                error: 'Model bulunamadı. Lütfen önce modeli indirin: ollama pull llama3.2' 
            });
        } else if (error.code === 'ECONNREFUSED') {
            res.status(500).json({ 
                error: 'Ollama sunucusuna bağlanılamıyor. Ollama\'nın çalıştığından emin olun.' 
            });
        } else {
            res.status(500).json({ 
                error: 'Bir hata oluştu: ' + error.message 
            });
        }
    }
});

// Mevcut modelleri listele
app.get('/models', async (req, res) => {
    try {
        const models = await ollama.list();
        res.json(models);
    } catch (error) {
        console.error('Model listesi alınamadı:', error);
        res.status(500).json({ error: 'Model listesi alınamadı' });
    }
});

// Sunucuyu başlat
app.listen(port, () => {
    console.log(`🤖 Ollama Chatbot http://localhost:${port} adresinde çalışıyor`);
    console.log('💡 Kullanım öncesi Ollama\'nın çalıştığından ve bir model indirdiğinizden emin olun:');
    console.log('   ollama pull llama3.2');
});
