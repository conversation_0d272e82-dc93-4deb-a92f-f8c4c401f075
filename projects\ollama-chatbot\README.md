# Olla<PERSON> Chatbot

Ollama ile basit ve kullanıcı dostu bir sohbet botu uygulaması. Hem masaüstü hem de web versiyonu mevcut.

## Özellikler

- 🤖 Ollama ile AI sohbet
- 🖥️ **Masaüstü uygulaması** (Electron)
- 💬 Modern ve responsive arayüz
- ⚡ Gerçek zamanlı mesajlaşma
- 🎨 Güzel tasarım ve animasyonlar
- 📱 Mobil uyumlu web versiyonu
- 🔄 Otomatik sunucu yönetimi
- ⌨️ Klavye kısayolları

## Gereksinimler

- Node.js (v14 veya üzeri)
- Ollama (yüklü ve çalışır durumda)

## Kurulum

1. **Ollama'yı yükleyin:**
   ```bash
   # Windows için: https://ollama.ai/download
   # macOS için: brew install ollama
   # Linux için: curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **Bir model indirin:**
   ```bash
   ollama pull qwen3:4b
   ```

3. **<PERSON><PERSON><PERSON>'yı başlatın:**
   ```bash
   ollama serve
   ```

4. **Proje bağımlılıklarını yükleyin:**
   ```bash
   npm install
   ```

## Kullanım

### Masaüstü Uygulaması (Önerilen)

1. **Masaüstü uygulamasını başlatın:**
   ```bash
   npm start
   ```

2. **Uygulama otomatik olarak açılacak!**

### Web Versiyonu

1. **Web sunucusunu başlatın:**
   ```bash
   npm run web
   ```

2. **Tarayıcınızda açın:**
   ```
   http://localhost:3000
   ```

3. **Sohbet etmeye başlayın!**

## Geliştirme

Geliştirme modunda çalıştırmak için:

```bash
npm run dev
```

Bu komut nodemon kullanarak dosya değişikliklerini otomatik olarak algılar.

## Desteklenen Modeller

Bu uygulama Ollama'nın desteklediği tüm modellerle çalışır:

- qwen3:4b (varsayılan)
- llama3.2
- llama3.1
- mistral
- codellama
- ve diğerleri...

Model değiştirmek için `index.js` dosyasındaki `model` parametresini düzenleyebilirsiniz.

## API Endpoints

- `GET /` - Ana sayfa
- `POST /chat` - Mesaj gönderme
- `GET /models` - Mevcut modelleri listele

## Sorun Giderme

### "Model bulunamadı" hatası
```bash
ollama pull qwen3:4b
```

### "Ollama sunucusuna bağlanılamıyor" hatası
```bash
ollama serve
```

### Port 3000 kullanımda
`index.js` dosyasında port numarasını değiştirin.

## Lisans

MIT
