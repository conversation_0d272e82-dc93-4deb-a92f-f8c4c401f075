# Olla<PERSON> Chatbot

Ollama ile basit ve kullanıcı dostu bir sohbet botu uygulaması.

## Özellikler

- 🤖 Ollama ile AI sohbet
- 💬 Modern ve responsive web arayüzü
- ⚡ Gerç<PERSON> zamanlı mesajlaşma
- 🎨 Güzel tasarım ve animasyonlar
- 📱 Mobil uyumlu

## Gereksinimler

- Node.js (v14 veya üzeri)
- Ollama (yüklü ve çalışır durumda)

## Kurulum

1. **Ollama'yı yükleyin:**
   ```bash
   # Windows için: https://ollama.ai/download
   # macOS için: brew install ollama
   # Linux için: curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **Bir model indirin:**
   ```bash
   ollama pull llama3.2
   ```

3. **Ollama'yı başlatın:**
   ```bash
   ollama serve
   ```

4. **Proje bağımlılıklarını yükleyin:**
   ```bash
   npm install
   ```

## Kullanım

1. **Uygulamayı başlatın:**
   ```bash
   npm start
   ```

2. **Tarayıcınızda açın:**
   ```
   http://localhost:3000
   ```

3. **Sohbet etmeye başlayın!**

## Geliştirme

Geliştirme modunda çalıştırmak için:

```bash
npm run dev
```

Bu komut nodemon kullanarak dosya değişikliklerini otomatik olarak algılar.

## Desteklenen Modeller

Bu uygulama Ollama'nın desteklediği tüm modellerle çalışır:

- llama3.2 (varsayılan)
- llama3.1
- mistral
- codellama
- ve diğerleri...

Model değiştirmek için `index.js` dosyasındaki `model` parametresini düzenleyebilirsiniz.

## API Endpoints

- `GET /` - Ana sayfa
- `POST /chat` - Mesaj gönderme
- `GET /models` - Mevcut modelleri listele

## Sorun Giderme

### "Model bulunamadı" hatası
```bash
ollama pull llama3.2
```

### "Ollama sunucusuna bağlanılamıyor" hatası
```bash
ollama serve
```

### Port 3000 kullanımda
`index.js` dosyasında port numarasını değiştirin.

## Lisans

MIT
