{"name": "ollama-chatbot", "version": "1.0.0", "main": "index.js", "scripts": {"prepare": "node -e \"try{require('fs').accessSync('index.js')}catch(e){require('fs').writeFileSync('index.js','')}\"", "dev": "nodemon index.js", "start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"dotenv": "^17.2.2", "express": "^5.1.0"}, "devDependencies": {"nodemon": "^3.1.7"}}