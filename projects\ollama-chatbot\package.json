{"name": "ollama-chatbot", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON> ile basit sohbet botu", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "web": "node index.js", "web-dev": "nodemon index.js", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["ollama", "chatbot", "ai"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "ollama": "^0.5.0"}, "devDependencies": {"electron": "^38.1.2", "electron-builder": "^26.0.12", "nodemon": "^3.1.7"}, "build": {"appId": "com.ollama.chatbot", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["main.js", "public/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}